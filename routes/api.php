<?php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\UnaAuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\PostsController;


// USER
Route::post('/login', [UnaAuthController::class, 'login']);
Route::post('/check-session', [UnaAuthController::class, 'checkSession']);
Route::post('/logout', [UnaAuthController::class, 'logout']);
Route::middleware('auth:sanctum')->group(function () {
    // Secured
    Route::get('/user', [UnaAuthController::class, 'user']);

    //POSTS
    Route::post('/post', [PostsController::class, 'store']);
    Route::get('/post', [PostsController::class, 'index']);
});

